import { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from './ui/card'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from './ui/select'
import { Button } from './ui/button'
import { Textarea } from './ui/textarea'
import { ArrowLeft, Gift, Users, CheckCircle, AlertCircle, Loader2 } from 'lucide-react'
import { apiService } from '../services/api'

interface UserRewardManagerProps {
  onBackToPages: () => void
}

interface RewardResult {
  successfulPhones: string[]
  notFoundPhones: string[]
  totalProcessed: number
  successCount: number
}

const rewardOptions = [
  { value: '3_days_reward', label: '3天奖励', description: '发放3天会员奖励' },
  { value: '7_days_reward', label: '7天奖励', description: '发放7天会员奖励' },
  { value: '10_days_reward', label: '10天奖励', description: '发放10天会员奖励' },
  { value: '30_days_reward', label: '30天奖励', description: '发放30天会员奖励' }
]

export function UserRewardManager({ onBackToPages }: UserRewardManagerProps) {
  const [selectedRewardType, setSelectedRewardType] = useState<string>('')
  const [phoneNumbers, setPhoneNumbers] = useState<string>('')
  const [isLoading, setIsLoading] = useState(false)
  const [result, setResult] = useState<RewardResult | null>(null)
  const [error, setError] = useState<string>('')

  const handleSubmit = async () => {
    if (!selectedRewardType) {
      setError('请选择奖励类型')
      return
    }

    if (!phoneNumbers.trim()) {
      setError('请输入手机号码')
      return
    }

    // 解析手机号列表
    const phoneList = phoneNumbers
      .split('\n')
      .map(phone => phone.trim())
      .filter(phone => phone.length > 0)

    if (phoneList.length === 0) {
      setError('请输入有效的手机号码')
      return
    }

    setIsLoading(true)
    setError('')
    setResult(null)

    try {
      const response = await apiService.grantBatchRewards(selectedRewardType, phoneList)
      
      if (response.success && response.data) {
        setResult(response.data as RewardResult)
      } else {
        setError(response.message || '发放奖励失败')
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : '发放奖励失败')
    } finally {
      setIsLoading(false)
    }
  }

  const handleReset = () => {
    setSelectedRewardType('')
    setPhoneNumbers('')
    setResult(null)
    setError('')
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 p-6">
      <div className="max-w-4xl mx-auto">
        {/* 头部导航 */}
        <div className="flex items-center gap-4 mb-8">
          <Button
            variant="ghost"
            size="sm"
            onClick={onBackToPages}
            className="flex items-center gap-2 text-gray-600 hover:text-gray-900"
          >
            <ArrowLeft className="w-4 h-4" />
            返回页面列表
          </Button>
        </div>

        {/* 页面标题 */}
        <div className="text-center mb-8">
          <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br from-orange-500 to-red-600 rounded-2xl shadow-xl mb-4">
            <Gift className="w-8 h-8 text-white" />
          </div>
          <h1 className="text-3xl font-bold bg-gradient-to-r from-gray-900 via-orange-800 to-red-900 bg-clip-text text-transparent mb-2">
            用户奖励管理
          </h1>
          <p className="text-gray-600">
            为指定用户批量发放会员奖励
          </p>
        </div>

        {/* 主要内容 */}
        <Card className="shadow-xl border-0 bg-white/80 backdrop-blur-sm">
          <CardHeader className="pb-6">
            <CardTitle className="flex items-center gap-2 text-xl">
              <Users className="w-5 h-5 text-orange-600" />
              批量奖励发放
            </CardTitle>
            <CardDescription>
              选择奖励类型并输入用户手机号码，系统将为这些用户发放相应的会员奖励
            </CardDescription>
          </CardHeader>

          <CardContent className="space-y-6">
            {/* 奖励类型选择 */}
            <div className="space-y-2">
              <label className="text-sm font-medium text-gray-700">
                奖励类型 <span className="text-red-500">*</span>
              </label>
              <Select value={selectedRewardType} onValueChange={setSelectedRewardType}>
                <SelectTrigger className="h-12">
                  <SelectValue placeholder="请选择奖励类型..." />
                </SelectTrigger>
                <SelectContent>
                  {rewardOptions.map(option => (
                    <SelectItem key={option.value} value={option.value}>
                      <div className="flex flex-col">
                        <span className="font-medium">{option.label}</span>
                        <span className="text-xs text-gray-500">{option.description}</span>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* 手机号输入 */}
            <div className="space-y-2">
              <label className="text-sm font-medium text-gray-700">
                用户手机号码 <span className="text-red-500">*</span>
              </label>
              <Textarea
                value={phoneNumbers}
                onChange={(e) => setPhoneNumbers(e.target.value)}
                placeholder="请输入手机号码，每行一个&#10;例如：&#10;13800138000&#10;13900139000&#10;14000140000"
                className="min-h-[120px] resize-none"
                disabled={isLoading}
              />
              <p className="text-xs text-gray-500">
                每行输入一个手机号码，支持批量输入
              </p>
            </div>

            {/* 错误提示 */}
            {error && (
              <div className="flex items-center gap-2 p-3 bg-red-50 border border-red-200 rounded-lg text-red-700">
                <AlertCircle className="w-4 h-4 flex-shrink-0" />
                <span className="text-sm">{error}</span>
              </div>
            )}

            {/* 操作按钮 */}
            <div className="flex gap-3 pt-4">
              <Button
                onClick={handleSubmit}
                disabled={isLoading || !selectedRewardType || !phoneNumbers.trim()}
                className="flex-1 h-12 bg-gradient-to-r from-orange-500 to-red-600 hover:from-orange-600 hover:to-red-700"
              >
                {isLoading ? (
                  <>
                    <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                    发放中...
                  </>
                ) : (
                  <>
                    <Gift className="w-4 h-4 mr-2" />
                    发放奖励
                  </>
                )}
              </Button>
              <Button
                variant="outline"
                onClick={handleReset}
                disabled={isLoading}
                className="px-6 h-12"
              >
                重置
              </Button>
            </div>

            {/* 结果显示 */}
            {result && (
              <div className="mt-6 p-4 bg-gray-50 rounded-lg border">
                <h3 className="font-medium text-gray-900 mb-3 flex items-center gap-2">
                  <CheckCircle className="w-5 h-5 text-green-600" />
                  发放结果
                </h3>
                
                <div className="grid grid-cols-2 gap-4 mb-4">
                  <div className="text-center p-3 bg-green-50 rounded-lg border border-green-200">
                    <div className="text-2xl font-bold text-green-600">{result.successCount}</div>
                    <div className="text-sm text-green-700">成功发放</div>
                  </div>
                  <div className="text-center p-3 bg-gray-100 rounded-lg border border-gray-200">
                    <div className="text-2xl font-bold text-gray-600">{result.notFoundPhones.length}</div>
                    <div className="text-sm text-gray-700">未找到用户</div>
                  </div>
                </div>

                {result.successfulPhones.length > 0 && (
                  <div className="mb-4">
                    <h4 className="font-medium text-green-700 mb-2">成功发放的手机号：</h4>
                    <div className="max-h-32 overflow-y-auto bg-white p-2 rounded border text-sm">
                      {result.successfulPhones.join(', ')}
                    </div>
                  </div>
                )}

                {result.notFoundPhones.length > 0 && (
                  <div>
                    <h4 className="font-medium text-orange-700 mb-2">未找到的手机号：</h4>
                    <div className="max-h-32 overflow-y-auto bg-white p-2 rounded border text-sm">
                      {result.notFoundPhones.join(', ')}
                    </div>
                  </div>
                )}
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
