services:
  backend:
    container_name: backend
    build:
      context: ./backend
      target: production
    restart: always
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
    depends_on:
      - mysql
      - redis
    env_file:
      - .env.production
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
    networks:
      - app-prod-network

  admin-frontend:
    container_name: admin-frontend-prod
    build:
      context: ./admin-frontend
      target: production
    ports:
      - "5174:80"
    restart: always
    networks:
      - app-prod-network

  nginx:
    container_name: nginx
    image: nginx:alpine
    restart: always
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/conf.d:/etc/nginx/conf.d
      - /etc/letsencrypt:/etc/letsencrypt:ro  # 挂载整个Let's Encrypt目录（只读）
    depends_on:
      - backend
      - admin-frontend
    networks:
      - app-prod-network

  mysql:
    container_name: mysql
    image: mysql:8.0
    restart: always
    env_file:
      - .env.production
    environment:
      - MYSQL_ROOT_PASSWORD=${DB_PASSWORD}
      - MYSQL_DATABASE=${DB_NAME}
      - MYSQL_USER=${DB_USER}
      - MYSQL_PASSWORD=${DB_PASSWORD}
    volumes:
      - mysql_prod_data:/var/lib/mysql
      - ./mysql/init:/docker-entrypoint-initdb.d
    ports:
      - "3306:3306"
    command: --default-authentication-plugin=mysql_native_password --character-set-server=utf8mb4 --collation-server=utf8mb4_unicode_ci --default-time-zone='+08:00'
    networks:
      - app-prod-network

  redis:
    container_name: redis
    image: redis:alpine
    restart: always
    ports:
      - "6379:6379"
    volumes:
      - redis_prod_data:/data
    networks:
      - app-prod-network

networks:
  app-prod-network:
    driver: bridge
    name: my-app-prod-network

volumes:
  mysql_prod_data:
  redis_prod_data: 