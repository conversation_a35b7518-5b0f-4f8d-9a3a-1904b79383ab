import { QueryTypes, Transaction } from 'sequelize';
import sequelize from '../config/sequelize';
import logger from '../utils/logger';
import { UserMembershipStatus } from '../types/membership.type';
import { MembershipType } from '../enums';
import { RedisService } from './redis.service';
import { log } from 'console';


export class UserMembershipOptimizedService {

  /**
   * 计算过期日期，设置为过期当天的23:59:59
   * @param startDate 开始日期
   * @param days 天数
   * @returns 过期日期（当天23:59:59）
   */
  private static calculateExpireDate(startDate: Date, days: number): Date {
    const expireDate = new Date(startDate);
    expireDate.setDate(expireDate.getDate() + days);
    // 设置为当天的23:59:59
    expireDate.setHours(23, 59, 59, 999);
    return expireDate;
  }

  /**
   * 计算奖励天数的到期日期（基于现有未过期奖励记录累加）
   * @param userId 用户ID
   * @param rewardDays 奖励天数
   * @param now 当前时间
   * @param transaction 数据库事务
   * @param logPrefix 日志前缀（可选）
   * @returns 计算后的到期日期
   */
  private static async calculateRewardExpireDate(
    userId: number,
    rewardDays: number,
    now: Date,
    transaction: Transaction,
    logPrefix: string = ''
  ): Promise<Date> {
    // 查询用户未过期的奖励记录
    const existingActiveRewards = await sequelize.query(`
      SELECT * FROM user_rewards 
      WHERE user_id = :userId 
        AND status = 'GRANTED'
        AND expire_date > :now
      ORDER BY expire_date DESC
    `, {
      replacements: { userId, now },
      type: QueryTypes.SELECT,
      transaction
    }) as any[];

    logger.info(`existingActiveRewards: ${JSON.stringify(existingActiveRewards)}`);

    let expireDate: Date;
    const prefix = logPrefix ? `${logPrefix}：` : '';

    if (existingActiveRewards.length > 0) {
      // 有未过期的奖励记录，在最晚的到期时间基础上累加
      const latestExpireDate = new Date(existingActiveRewards[0].expire_date);
      expireDate = this.calculateExpireDate(latestExpireDate, rewardDays);
      logger.info(`${prefix}在现有奖励记录基础上累加天数 (userId: ${userId}, 原到期时间: ${latestExpireDate.toLocaleString('zh-CN')}, 新到期时间: ${expireDate.toLocaleString('zh-CN')})`);
    } else {
      // 没有未过期的奖励记录，以当前时间为基础计算
      expireDate = this.calculateExpireDate(now, rewardDays);
      logger.info(`${prefix}以当前时间为基础计算奖励天数 (userId: ${userId}, 到期时间: ${expireDate.toLocaleString('zh-CN')})`);
    }

    return expireDate;
  }

  /**
   * 获取用户当前会员状态
   * @param userId 用户ID
   * @returns 用户会员状态
   */
  static async getUserMembershipStatus(userId: number): Promise<UserMembershipStatus> {
    try {
      // 直接计算会员状态
      const status = await this.calculateMembershipStatus(userId);
      return status;
    } catch (error) {
      logger.error(`获取用户会员状态失败 (userId: ${userId}):`, error);
      throw error;
    }
  }

  /**
   * 计算用户当前会员状态
   * @param userId 用户ID
   * @returns 计算后的会员状态
   */
  private static async calculateMembershipStatus(userId: number): Promise<UserMembershipStatus> {
    const now = new Date();

    // 查询用户的付费会员记录
    const premiumMemberships = await sequelize.query(`
      SELECT upm.*, mp.renewal_type, mp.name as plan_name
      FROM user_paid_memberships upm
      JOIN membership_plans mp ON upm.plan_id = mp.id
      WHERE upm.user_id = :userId 
        AND upm.status = 'ACTIVE'
        AND upm.expire_date > :now
      ORDER BY upm.expire_date DESC
    `, {
      replacements: { userId, now },
      type: QueryTypes.SELECT
    }) as any[];

    // 查询用户的奖励天数记录（排除已合并的记录）
    const rewardDays = await sequelize.query(`
      SELECT ur.*, rt.name as reward_name
      FROM user_rewards ur
      JOIN reward_types rt ON ur.reward_type_id = rt.id
      WHERE ur.user_id = :userId 
        AND ur.status = 'GRANTED'
        AND ur.expire_date > :now
      ORDER BY ur.expire_date DESC
    `, {
      replacements: { userId, now },
      type: QueryTypes.SELECT
    }) as any[];

    // 计算付费会员状态
    const activePremium = premiumMemberships.length > 0 ? premiumMemberships[0] : null;
    const premiumExpireDate = activePremium ? new Date(activePremium.expire_date) : null;
    
    // 计算奖励天数状态
    const activeRewards = rewardDays.filter(reward => new Date(reward.expire_date) > now);
    const trialExpireDate = activeRewards.length > 0 
      ? new Date(Math.max(...activeRewards.map(r => new Date(r.expire_date).getTime())))
      : null;

    // 确定会员类型和到期时间（优先级：PREMIUM > TRIAL > GUEST）
    let membershipType: MembershipType;
    let expireDate: Date | null;
    let isAutoRenew = false;

    if (activePremium) {
      // 有有效的付费会员
      if (activePremium.renewal_type === 'LIFETIME') {
        membershipType = MembershipType.LIFETIME;
        expireDate = null; // 终身会员没有到期时间
      } else {
        membershipType = MembershipType.PREMIUM;
        expireDate = premiumExpireDate;
        isAutoRenew = activePremium.renewal_type === 'AUTO_RENEW' && activePremium.is_renewal_active;
      }
    } else if (trialExpireDate) {
      // 有有效的体验天数
      membershipType = MembershipType.TRIAL;
      expireDate = trialExpireDate;
    } else {
      // 游客
      membershipType = MembershipType.GUEST;
      expireDate = null;
    }

    // 计算剩余有效天数
    const remainingDays = this.calculateRemainingDays(expireDate, now);

    return {
      userId,
      membershipType,
      expireDate,
      isAutoRenew,
      premiumExpireDate,
      trialExpireDate,
      displayText: this.getDisplayText(membershipType, expireDate, isAutoRenew),
      isActive: membershipType !== MembershipType.GUEST,
      remainingDays
    };
  }

  /**
   * 计算剩余有效天数
   * @param expireDate 到期时间
   * @param now 当前时间
   * @returns 剩余天数，null表示无限期
   */
  private static calculateRemainingDays(expireDate: Date | null, now: Date): number | null {
    if (!expireDate) {
      // 终身会员或无到期时间
      return null;
    }

    // 计算到期时间和当前时间的毫秒差
    const timeDiff = expireDate.getTime() - now.getTime();
    
    // 如果已过期，返回0
    if (timeDiff <= 0) {
      return 0;
    }

    // 转换为天数，向上取整（保证当天也算1天）
    const daysDiff = Math.ceil(timeDiff / (24 * 60 * 60 * 1000));
    
    return daysDiff;
  }

  /**
   * 获取会员状态的显示文本
   */
  private static getDisplayText(type: MembershipType, expireDate: Date | null, isAutoRenew: boolean): string {
    switch (type) {
      case MembershipType.LIFETIME:
        return '永久会员';
      case MembershipType.PREMIUM:
        return isAutoRenew ? '会员生效中（自动续费）' : '会员生效中';
      case MembershipType.TRIAL:
        return '体验会员';
      case MembershipType.GUEST:
      default:
        return '游客';
    }
  }

  /**
   * 为用户购买付费会员
   * @param userId 用户ID
   * @param planId 会员计划ID
   * @param orderId 订单ID
   * @param transaction 数据库事务
   */
  static async purchaseMembership(
    userId: number, 
    planId: number, 
    orderId: number,
    transaction?: Transaction
  ): Promise<void> {
    const useExternalTransaction = !!transaction;
    const t = transaction || await sequelize.transaction();

    // 分布式锁的键名和超时时间（30秒）
    const lockKey = `purchase_membership_lock:${userId}`;
    const lockTimeout = 30;
    const lockValue = `${Date.now()}-${Math.random()}`;

    // 尝试获取分布式锁
    const lockAcquired = await RedisService.setNX(lockKey, lockValue, lockTimeout);
    if (!lockAcquired) {
      throw new Error('用户正在处理其他会员购买请求，请稍后再试');
    }

    try {
      // 1. 检查用户是否已有有效的付费会员，防止重复购买
      const existingPaidMemberships = await sequelize.query(`
        SELECT upm.*, mp.renewal_type, mp.name as plan_name
        FROM user_paid_memberships upm
        JOIN membership_plans mp ON upm.plan_id = mp.id
        WHERE upm.user_id = :userId 
          AND upm.status = 'ACTIVE'
          AND upm.expire_date > :now
        ORDER BY upm.expire_date DESC
      `, {
        replacements: { userId, now: new Date() },
        type: QueryTypes.SELECT,
        transaction: t
      }) as any[];

      if (existingPaidMemberships.length > 0) {
        const activeMembership = existingPaidMemberships[0];
        const expireDate = new Date(activeMembership.expire_date).toLocaleString('zh-CN');        
        logger.info(`用户已有有效的付费会员 (${activeMembership.plan_name})，到期时间: ${expireDate}，不能重复购买`);
        return;
      }

      // 2. 获取会员计划信息
      const plan = await sequelize.query(`
        SELECT * FROM membership_plans WHERE id = :planId AND is_active = TRUE
      `, {
        replacements: { planId },
        type: QueryTypes.SELECT,
        transaction: t
      }) as any[];

      if (plan.length === 0) {
        throw new Error(`会员计划不存在或已停用 (planId: ${planId})`);
      }

      const membershipPlan = plan[0];
      const now = new Date();
      let expireDate: Date;

      if (membershipPlan.renewal_type === 'LIFETIME') {
        // 终身会员，设置一个很远的未来时间
        expireDate = new Date('2099-12-31 23:59:59');
      } else {
        // 计算到期时间，设置为过期当天的23:59:59
        expireDate = this.calculateExpireDate(now, membershipPlan.duration_days);
      }

      // 3. 检查用户是否有未过期的体验天数，如果有则合并到购买套餐中
      let mergedRewardDays = 0;
      const activeRewards = await sequelize.query(`
        SELECT ur.*, rt.name as reward_name
        FROM user_rewards ur
        JOIN reward_types rt ON ur.reward_type_id = rt.id
        WHERE ur.user_id = :userId 
          AND ur.status = 'GRANTED'
          AND ur.expire_date > :now
        ORDER BY ur.expire_date DESC
      `, {
        replacements: { userId, now },
        type: QueryTypes.SELECT,
        transaction: t
      }) as any[];

      if (activeRewards.length > 0 && membershipPlan.renewal_type !== 'LIFETIME') {
        // 计算所有未过期奖励的剩余天数总和
        const totalRemainingDays = activeRewards.reduce((total, reward) => {
          const rewardExpireDate = new Date(reward.expire_date);
          const remainingMs = rewardExpireDate.getTime() - now.getTime();
          const remainingDays = Math.floor(remainingMs / (24 * 60 * 60 * 1000));
          return total + remainingDays;
        }, 0);

        if (totalRemainingDays > 0) {
          mergedRewardDays = totalRemainingDays;
          // 将剩余天数添加到购买套餐的到期时间中，保持23:59:59格式
          expireDate = this.calculateExpireDate(now, membershipPlan.duration_days + mergedRewardDays);

          // 将所有相关的体验天数记录状态更新为MERGED
          const rewardIds = activeRewards.map(r => r.id);
          await sequelize.query(`
            UPDATE user_rewards 
            SET status = 'MERGED', 
                merge_at = :mergeAt,
                merge_reason = :mergeReason,
                updated_at = NOW()
            WHERE id IN (:rewardIds)
          `, {
            replacements: {
              mergeAt: now,
              mergeReason: `合并到${membershipPlan.name}套餐 (订单ID: ${orderId})`,
              rewardIds
            },
            transaction: t
          });

          logger.info(`合并用户体验天数成功 (userId: ${userId}, 合并天数: ${mergedRewardDays}, 奖励记录数: ${activeRewards.length})`);
        }
      }

      // 4. 创建付费会员记录
      await sequelize.query(`
        INSERT INTO user_paid_memberships 
        (user_id, plan_id, start_date, expire_date, is_renewal_active, order_id)
        VALUES (:userId, :planId, :startDate, :expireDate, :isRenewalActive, :orderId)
      `, {
        replacements: {
          userId,
          planId,
          startDate: now,
          expireDate,
          isRenewalActive: membershipPlan.renewal_type === 'AUTO_RENEW',
          orderId
        },
        transaction: t
      });

      if (!useExternalTransaction) {
        await t.commit();
      }

      const logMessage = mergedRewardDays > 0 
        ? `用户购买会员成功 (userId: ${userId}, planId: ${planId}, orderId: ${orderId}, 合并体验天数: ${mergedRewardDays}天)`
        : `用户购买会员成功 (userId: ${userId}, planId: ${planId}, orderId: ${orderId})`;
      logger.info(logMessage);
    } catch (error) {
      if (!useExternalTransaction) {
        await t.rollback();
      }
      logger.error(`用户购买会员失败 (userId: ${userId}, planId: ${planId}):`, error);
      throw error;
    } finally {
      // 释放分布式锁
      try {
        await RedisService.del(lockKey);
        logger.debug(`释放购买会员锁成功 (userId: ${userId})`);
      } catch (lockError) {
        logger.error(`释放购买会员锁失败 (userId: ${userId}):`, lockError);
      }
    }
  }

  /**
   * 为用户添加奖励天数
   * @param userId 用户ID
   * @param rewardTypeKey 奖励类型
   * @param customDays 自定义天数（可选）
   * @param transaction 数据库事务
   * @param notes 备注信息（可选）
   */
  static async grantRewardDays(
    userId: number,
    rewardTypeKey: string,
    customDays?: number,
    transaction?: Transaction,
    notes?: string
  ): Promise<void> {
    const useExternalTransaction = !!transaction;
    const t = transaction || await sequelize.transaction();
    logger.info(`grantRewardDays: ${userId}, ${rewardTypeKey}, ${customDays}`);
    try {
      // 获取奖励类型信息
      const rewardType = await sequelize.query(`
        SELECT * FROM reward_types WHERE type_key = :rewardTypeKey AND is_active = TRUE
      `, {
        replacements: { rewardTypeKey },
        type: QueryTypes.SELECT,
        transaction: t
      }) as any[];

      if (rewardType.length === 0) {
        throw new Error(`奖励类型不存在或已停用 (rewardTypeKey: ${rewardTypeKey})`);
      }

      const reward = rewardType[0];
      logger.info(`reward: ${JSON.stringify(reward)}`);
      // 如果不可重复获得，检查是否已经获得过
      if (!reward.is_repeatable) {
        const existingReward = await sequelize.query(`
          SELECT COUNT(*) as count FROM user_rewards 
          WHERE user_id = :userId AND reward_type_id = :rewardTypeId AND (status = 'GRANTED' OR status = 'MERGED')
        `, {
          replacements: { userId, rewardTypeId: reward.id },
          type: QueryTypes.SELECT,
          transaction: t
        }) as any[];

        logger.info(`${userId} existingReward: ${JSON.stringify(existingReward)}`);
        if (existingReward[0].count > 0) {
          logger.info(`用户已获得过该奖励 (userId: ${userId}, rewardTypeKey: ${rewardTypeKey})`);
          return; // 已经获得过，不重复发放
        }
      }

      const rewardDays = customDays || reward.reward_days;
      const now = new Date();
      
      // 检查用户是否有有效的付费会员套餐
      const activePaidMemberships = await sequelize.query(`
        SELECT upm.*, mp.renewal_type, mp.name as plan_name
        FROM user_paid_memberships upm
        JOIN membership_plans mp ON upm.plan_id = mp.id
        WHERE upm.user_id = :userId 
          AND upm.status = 'ACTIVE'
          AND upm.expire_date > :now
        ORDER BY upm.expire_date DESC
      `, {
        replacements: { userId, now },
        type: QueryTypes.SELECT,
        transaction: t
      }) as any[];

      let rewardStatus: 'GRANTED' | 'MERGED' = 'GRANTED';
      let expireDate: Date;
      let mergeReason: string | null = null;

      if (activePaidMemberships.length > 0) {
        // 用户有有效的付费套餐，直接合并奖励天数到套餐中
        const activeMembership = activePaidMemberships[0];
        
        if (activeMembership.renewal_type !== 'LIFETIME') {
          logger.info("非终身会员延长时间");
          // 非终身会员才能延长时间
          const currentExpireDate = new Date(activeMembership.expire_date);
          const newExpireDate = this.calculateExpireDate(currentExpireDate, rewardDays);
          
          // 更新付费会员的到期时间
          await sequelize.query(`
            UPDATE user_paid_memberships 
            SET expire_date = :newExpireDate, updated_at = NOW()
            WHERE id = :membershipId
          `, {
            replacements: {
              newExpireDate,
              membershipId: activeMembership.id
            },
            transaction: t
          });

          // 设置奖励记录为已合并状态
          rewardStatus = 'MERGED';
          expireDate = newExpireDate; // 虽然已合并，但记录原本的到期时间用于追踪
          mergeReason = `奖励天数已合并到${activeMembership.plan_name}套餐 (会员ID: ${activeMembership.id})`;
          
          logger.info(`奖励天数已合并到付费套餐 (userId: ${userId}, 套餐: ${activeMembership.plan_name}, 奖励天数: ${rewardDays}, 新到期时间: ${newExpireDate.toLocaleString('zh-CN')})`);
        } else {
          logger.info(`终身会员，奖励天数无法合并到套餐中，但仍需正确记录奖励天数的累加逻辑`);
          // 终身会员，奖励天数无法合并到套餐中，但仍需正确记录奖励天数的累加逻辑
          expireDate = await this.calculateRewardExpireDate(userId, rewardDays, now, t, '终身会员');
        }
      } else {
          logger.info(`用户没有有效的付费套餐，按奖励累加逻辑处理`);
          // 用户没有有效的付费套餐，按奖励累加逻辑处理
          expireDate = await this.calculateRewardExpireDate(userId, rewardDays, now, t);
      }

      logger.info(`expireDate: ${expireDate}`);
      
      // 创建奖励记录
      await sequelize.query(`
        INSERT INTO user_rewards 
        (user_id, reward_type_id, reward_days, status, expire_date, notes, merge_at, merge_reason)
        VALUES (:userId, :rewardTypeId, :rewardDays, :status, :expireDate, :notes, :mergeAt, :mergeReason)
      `, {
        replacements: {
          userId,
          rewardTypeId: reward.id,
          rewardDays,
          status: rewardStatus,
          expireDate,
          notes: notes || null,
          mergeAt: rewardStatus === 'MERGED' ? now : null,
          mergeReason: mergeReason
        },
        transaction: t
      });

      if (!useExternalTransaction) {
        await t.commit();
      }

      const successMessage = rewardStatus === 'MERGED' 
        ? `发放奖励天数成功并已合并到付费套餐 (userId: ${userId}, rewardTypeKey: ${rewardTypeKey}, days: ${rewardDays})`
        : `发放奖励天数成功 (userId: ${userId}, rewardTypeKey: ${rewardTypeKey}, days: ${rewardDays})`;
      logger.info(successMessage);
    } catch (error) {
      if (!useExternalTransaction) {
        await t.rollback();
      }
      logger.error(`发放奖励天数失败 (userId: ${userId}, rewardTypeKey: ${rewardTypeKey}):`, error);
      throw error;
    }
  }

  /**
   * 批量更新过期的会员状态
   */
  static async updateExpiredMemberships(): Promise<void> {
    try {
      const now = new Date();

      // 更新过期的付费会员状态
      await sequelize.query(`
        UPDATE user_paid_memberships 
        SET status = 'EXPIRED', updated_at = NOW()
        WHERE status = 'ACTIVE' AND expire_date <= :now
      `, {
        replacements: { now }
      });

      logger.info('批量更新过期会员状态完成');
    } catch (error) {
      logger.error('批量更新过期会员状态失败:', error);
      throw error;
    }
  }
} 