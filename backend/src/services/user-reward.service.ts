import { UserReward, RewardType } from '../models';
import { RewardTypeService } from './reward-type.service';
import { RedisService } from './redis.service';
import { UserMembershipOptimizedService } from './user-membership-optimized.service';

import sequelize from '../config/sequelize';
import { RewardTypeKey } from '../enums';
import logger from '../utils/logger';
import { log } from 'console';

export class UserRewardService {

  private static readonly CACHE_KEY_PREFIX = 'user_reward:';

  /**
   * 检查用户是否已获得某种类型的奖励（直接查询数据库）
   */
  static async hasUserReceivedReward(userId: number, rewardTypeKey: string): Promise<boolean> {
    const rewardType = await RewardTypeService.getByTypeKey(rewardTypeKey);
    if (!rewardType) {
      return false;
    }

    const count = await UserReward.count({
      where: {
        user_id: userId,
        reward_type_id: rewardType.id,
        status: 'GRANTED',
      },
    });

    return count > 0;
  }

  /**
   * 通用的奖励发放方法（直接调用新的优化服务）
   * @param userId 用户ID
   * @param rewardTypeKey 奖励类型键
   * @param options 配置选项
   * @returns 发放的奖励记录，如果已经获得过则返回null
   */
  static async grantRewardWithTransaction(
    userId: number,
    rewardTypeKey: string,
    options?: {
      customRewardDays?: number;
      notes?: string;
      addToMembership?: boolean; // 已废弃，保留兼容性
      transaction?: any; // 外部事务
    }
  ): Promise<UserReward | null> {
    logger.info(`grantRewardWithTransaction: ${userId}, ${rewardTypeKey}`);
    
    try {
      // 直接调用优化后的服务
      await UserMembershipOptimizedService.grantRewardDays(
        userId,
        rewardTypeKey,
        options?.customRewardDays,
        options?.transaction,
        options?.notes
      );

      // 查询刚创建的奖励记录并返回
      const rewardType = await RewardTypeService.getByTypeKey(rewardTypeKey);
      if (!rewardType) {
        return null;
      }

      const reward = await UserReward.findOne({
        where: {
          user_id: userId,
          reward_type_id: rewardType.id,
          status: 'GRANTED',
        },
        order: [['created_at', 'DESC']],
      });

      // 清除相关缓存
      await this.clearUserRewardCache(userId);

      return reward;
    } catch (error) {
      logger.error(`发放奖励失败 (${rewardTypeKey}):`, error);
      throw error;
    }
  }

  /**
   * 为用户发放成就奖励
   * @param userId 用户ID
   * @param customRewardDays 自定义奖励天数（可选）
   * @param notes 备注信息（可选）
   * @returns 发放的奖励记录，如果已经获得过则返回null
   */
  static async grantAchievementReward(
    userId: number,
    customRewardDays?: number,
    notes?: string
  ): Promise<UserReward | null> {
    logger.info(`grantAchievementReward: ${userId}, ${customRewardDays}, ${notes}`);
    return this.grantRewardWithTransaction(userId, RewardTypeKey.ACHIEVEMENT, {
      customRewardDays,
      notes: notes || '达成成就奖励',
    });
  }

  /**
   * 为用户发放新用户奖励
   * @param userId 用户ID
   * @param customRewardDays 自定义奖励天数（可选）
   * @param notes 备注信息（可选）
   * @returns 发放的奖励记录，如果已经获得过则返回null
   */
  static async grantNewUserReward(
    userId: number,
    customRewardDays?: number,
    notes?: string
  ): Promise<UserReward | null> {
    logger.info(`grantNewUserReward: ${userId}, ${customRewardDays}, ${notes}`);
    return this.grantRewardWithTransaction(userId, RewardTypeKey.NEW_USER, {
      customRewardDays,
      notes: notes || '新用户注册奖励',
    });
  }

  /**
   * 为用户发放邀请奖励
   * @param userId 用户ID
   * @param customRewardDays 自定义奖励天数（可选）
   * @returns 发放的奖励记录
   */
  static async grantInviteReward(
    userId: number,
    customRewardDays?: number
  ): Promise<UserReward | null> {
    const notes = `邀请奖励`;

    logger.info(`grantInviteReward: ${userId}, ${customRewardDays}, ${notes}`);
    
    return this.grantRewardWithTransaction(userId, RewardTypeKey.INVITE_REWARD, {
      customRewardDays,
      notes,
    });
  }

  /**
   * 批量发放奖励（通用方法）
   * @param userIds 用户ID数组
   * @param rewardTypeKey 奖励类型键
   * @param options 配置选项
   * @returns 成功发放的奖励记录数组
   */
  static async grantRewardBatch(
    userIds: number[],
    rewardTypeKey: string,
    options?: {
      customRewardDays?: number;
      notes?: string;
      addToMembership?: boolean;
    }
  ): Promise<UserReward[]> {
    const results: UserReward[] = [];
    
    // 为了保证每个用户的奖励发放是原子操作，这里不使用全局事务
    // 而是为每个用户单独使用事务
    for (const userId of userIds) {
      try {
        const reward = await this.grantRewardWithTransaction(userId, rewardTypeKey, options);
        if (reward) {
          results.push(reward);
        }
      } catch (error) {
        console.error(`为用户 ${userId} 发放奖励失败 (${rewardTypeKey}):`, error);
      }
    }

    return results;
  }

  /**
   * 批量为用户发放成就奖励
   * @param userIds 用户ID数组
   * @param customRewardDays 自定义奖励天数（可选）
   * @param notes 备注信息（可选）
   * @returns 成功发放的奖励记录数组
   */
  static async grantAchievementRewardBatch(
    userIds: number[],
    customRewardDays?: number,
    notes?: string
  ): Promise<UserReward[]> {
    return this.grantRewardBatch(userIds, RewardTypeKey.ACHIEVEMENT, {
      customRewardDays,
      notes: notes || '达成成就奖励',
    });
  }

  /**
   * 批量为用户发放新用户奖励
   * @param userIds 用户ID数组
   * @param customRewardDays 自定义奖励天数（可选）
   * @param notes 备注信息（可选）
   * @returns 成功发放的奖励记录数组
   */
  static async grantNewUserRewardBatch(
    userIds: number[],
    customRewardDays?: number,
    notes?: string
  ): Promise<UserReward[]> {
    return this.grantRewardBatch(userIds, RewardTypeKey.NEW_USER, {
      customRewardDays,
      notes: notes || '新用户注册奖励',
    });
  }

  /**
   * 获取用户的奖励历史
   */
  static async getUserRewards(
    userId: number,
    options?: {
      status?: 'GRANTED' | 'MERGED';
      rewardTypeKey?: string;
      limit?: number;
      offset?: number;
    }
  ): Promise<{ rewards: UserReward[]; total: number }> {
    const whereClause: any = { user_id: userId };
    
    if (options?.status) {
      whereClause.status = options.status;
    }

    const includeClause: any[] = [
      {
        model: RewardType,
        as: 'reward_type',
        where: options?.rewardTypeKey ? { type_key: options.rewardTypeKey } : undefined,
      },
    ];

    const { count, rows } = await UserReward.findAndCountAll({
      where: whereClause,
      include: includeClause,
      order: [['created_at', 'DESC']],
      limit: options?.limit,
      offset: options?.offset,
    });

    return {
      rewards: rows,
      total: count,
    };
  }

  /**
   * 计算用户总的奖励天数（带缓存）
   */
  static async getUserTotalRewardDays(userId: number): Promise<number> {
    const cacheKey = `${this.CACHE_KEY_PREFIX}total_days:${userId}`;
    
    // 尝试从缓存获取
    const cached = await RedisService.getInt(cacheKey);
    if (cached !== null) {
      return cached;
    }

    const result = await UserReward.sum('reward_days', {
      where: {
        user_id: userId,
        status: 'GRANTED',
      },
    });

    const totalDays = result || 0;
    
    // 缓存结果，设置较短的TTL因为这个数据可能经常变化
    await RedisService.set(cacheKey, totalDays.toString(), 600); // 10分钟

    return totalDays;
  }

  /**
   * 清除用户奖励相关缓存
   */
  static async clearUserRewardCache(userId: number): Promise<void> {
    const keys = [
      `${this.CACHE_KEY_PREFIX}total_days:${userId}`,
    ];

    for (const key of keys) {
      await RedisService.del(key);
    }
  }
}
