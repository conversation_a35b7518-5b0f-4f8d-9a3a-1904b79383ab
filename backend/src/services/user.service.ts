import { AppError } from '../middlewares/error.middleware';
import User, { UserAttributes } from '../models/user.model';
import { UserFullProfile, WechatCode2SessionResult } from '../types';
import { MyConfig } from '../types/my.type';
import logger from '../utils/logger';
import { OssService } from './oss.service';
import { UserMembershipOptimizedService } from './user-membership-optimized.service';

export type UserUpdatePayload = Pick<UserAttributes, 'username' | 'avatarKey' | 'phone' | 'birthDate'>;
export class UserService {
  
  static async getOrCreateUser(wxResult: WechatCode2SessionResult): Promise<User> {
    let user = await User.findOne({ where: { openid: wxResult.openid } });

    if (!user) {
      user = await User.create({
        openid: wxResult.openid,
        unionid: wxResult.unionid
      });
    }
    return user;
  }

  /**
   * 更新用户个人资料
   * @param userId 用户ID
   * @param data 更新的数据 (username, avatarKey, phone)
   * @returns 更新后的用户信息 (排除敏感信息)
  */
  static async updateUserProfile(
    userId: number,
    data: Partial<UserUpdatePayload>
  ): Promise<Partial<UserAttributes>> {
    try{
      const user = await User.findByPk(userId);
      if (!user) {
        throw new AppError('User not found', 404);
      }

      // 只允许更新指定字段
      const allowedUpdates: (keyof UserUpdatePayload)[] = ['username', 'avatarKey', 'phone', 'birthDate'];
      const updates: Partial<UserAttributes> = {};

      for (const key of allowedUpdates) {
        if (data[key] !== undefined) {
          // 使用类型断言，因为我们知道 key 是 UserAttributes 的有效键
          (updates as any)[key] = data[key];
        }
      }

      if (Object.keys(updates).length === 0) {
            throw new AppError('No valid fields provided for update', 400);
      }

      await user.update(updates);

      // 返回更新后的、排除敏感字段的用户信息
      const updatedUserProfile = await UserService.getUserProfile(userId);
      return updatedUserProfile; 
    }catch(error){
      logger.error(`更新用户资料失败: ${error}`);
      throw new AppError('更新用户资料失败', 500);
    }
    
  };

  static async updateOnboarding(userId: number, onboarding: boolean): Promise<User> {
    const user = await User.findByPk(userId);
    if (!user) {
      throw new AppError('User not found', 404);
    }
    const updatedUser = await user.update({ onboarding });
    return updatedUser;
  }

  /**
 * 获取用户个人资料
 * @param userId 用户ID
 * @returns 用户信息 (排除敏感信息如 openid, unionid)
 */
  static async getUserProfile(userId: number): Promise<Partial<UserAttributes>> {
    const user = await User.findByPk(userId, {
      attributes: { exclude: ['openid', 'unionid'] }, // 排除敏感字段
    });
    if (!user) {
      throw new AppError('User not found', 404);
    }
    return user.toJSON();
  };

  
  static async getUserFullProfile(userId: number): Promise<UserFullProfile> {
    const [user,membership, config] = await Promise.all([
      UserService.getUserProfile(userId),
      UserMembershipOptimizedService.getUserMembershipStatus(userId),
      OssService.getObjectJsonContentWithCache<MyConfig>('my')
    ]);
    if(!user.username){
      user.username = config.defaultUsername;
    }
    if(user.avatarKey){
      user.avatarKey = OssService.generateResourceUrl(user.avatarKey);
    }
    return { ...user, membership };
  }

  /**
   * 获取用户的openid (用于微信支付)
   * @param userId 用户ID
   * @returns 用户的openid
   */
  static async getUserOpenid(userId: number): Promise<string> {
    const user = await User.findByPk(userId, {
      attributes: ['openid'],
    });
    if (!user) {
      throw new AppError('User not found', 404);
    }
    return user.openid;
  }

}
    

